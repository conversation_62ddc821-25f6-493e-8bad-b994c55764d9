import requests
import json
from datetime import datetime

url = "https://apphwhq.longhuvip.com/w1/api/index.php"

payload = {
  'Order': "0",
  'a': "DailyLimitPerformance",
  'st': "2000",
  'apiv': "w41",
  'Type': "4",
  'c': "HomeDingPan",
  'PhoneOSNew': "1",
  'DeviceID': "6544d68e-fb4b-3b53-8d56-8abba41841ec",
  'VerSion': "********",
  'Index': "0",
  'PidType': "1",
  '': ""
}

headers = {
  'User-Agent': "Dalvik/2.1.0 (Linux; U; Android 13; Mi MIX 2S Build/TQ3A.230901.001)",
  'Connection': "Keep-Alive",
  'Accept-Encoding': "gzip"
}

def format_stock_data(data):
    """格式化股票数据输出"""
    print("=" * 120)
    print(f"{'📈 涨停板股票数据':^120}")
    print("=" * 120)

    # 表头
    print(f"{'股票代码':<10} {'股票名称':<12} {'概念':<25} {'涨停时间':<12} {'成交额(万)':<12} {'涨幅%':<8} {'连板':<8}")
    print("-" * 120)

    # 数据行
    for stock in data:
        code = stock[0]  # 股票代码
        name = stock[1]  # 股票名称
        limit_time = stock[4]  # 涨停时间戳
        concept_detail = stock[12]  # 详细概念
        market_value = stock[9] if len(stock) > 9 else 0  # 市值
        change_pct = stock[10] if len(stock) > 10 else 0  # 涨幅
        continuous_info = stock[16] if len(stock) > 16 and stock[16] else ""  # 连板信息

        # 格式化涨停时间
        if limit_time:
            time_str = datetime.fromtimestamp(limit_time).strftime("%H:%M:%S")
        else:
            time_str = "未知"

        # 截断过长的概念名称
        display_concept = concept_detail[:23] + "..." if len(concept_detail) > 23 else concept_detail

        # 格式化市值（转换为万元）
        volume_wan = round(market_value / 10000, 2) if market_value > 0 else 0

        # 格式化涨幅
        change_display = f"{change_pct:.2f}" if isinstance(change_pct, (int, float)) and change_pct > 0 else "涨停"

        # 格式化连板信息
        continuous_display = continuous_info if continuous_info else "-"

        print(f"{code:<10} {name:<12} {display_concept:<25} {time_str:<12} {volume_wan:<12} {change_display:<8} {continuous_display:<8}")

def main():
    """主函数"""
    try:
        print("🔄 正在获取涨停板数据...")
        response = requests.post(url, data=payload, headers=headers)

        # 检查HTTP状态码
        if response.status_code != 200:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            return

        # 解析JSON数据
        try:
            data = json.loads(response.text)
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {e}")
            print("原始响应:", response.text[:200] + "..." if len(response.text) > 200 else response.text)
            return

        # 检查API返回的错误码
        if data.get('errcode') != '0':
            print(f"❌ API返回错误，错误码: {data.get('errcode')}")
            return

        # 提取股票数据
        info = data.get('info', [])
        if not info or len(info) < 1:
            print("❌ 没有获取到股票数据")
            return

        stock_list = info[0]  # 股票列表
        date_info = info[1] if len(info) > 1 else "未知日期"  # 日期信息

        print(f"✅ 数据获取成功！日期: {date_info}")
        print(f"📊 共获取到 {len(stock_list)} 只涨停股票")

        # 格式化输出股票数据
        format_stock_data(stock_list)

        print("=" * 120)
        print(f"⏱️  请求耗时: {data.get('ttag', 0):.3f}秒")
        print("🎯 数据来源: 龙虎榜API")

    except requests.exceptions.RequestException as e:
        print(f"❌ 网络请求异常: {e}")
    except Exception as e:
        print(f"❌ 程序执行异常: {e}")

if __name__ == "__main__":
    main()